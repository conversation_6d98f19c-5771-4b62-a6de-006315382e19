'use client';

import React from 'react';

/**
 * Behavior requested:
 * - When user clicks Download, open /api/download/[fileId] in a new tab so the request starts there.
 * - Then hard-reload the current /d page ~300–600ms later to clear the Network panel in this tab.
 * - Optionally attempt to auto-close the new tab (best-effort).
 */
export default function DownloadButton({
  expired,
  apiDownload,
}: {
  expired: boolean;
  apiDownload: string;
}) {
  const handleClick = React.useCallback(() => {
    if (expired) return;

    // Open the download in a new tab so the request originates there.
    const w = window.open(apiDownload, '_blank', 'noopener');

    // Best-effort: try to close the new tab shortly after.
    if (w) {
      setTimeout(() => {
        try { w.close(); } catch {}
      }, 600);
    }

    // After a short delay, force a hard reload of the current /d page
    // to clear the Network panel entries in this tab.
    setTimeout(() => {
      try {
        // Hard reload bypassing client-side cache
        window.location.reload();
      } catch {
        // As a fallback, do a navigation to the same URL
        window.location.href = window.location.href;
      }
    }, 300);
  }, [expired, apiDownload]);

  return (
    <button
      type="button"
      onClick={handleClick}
      className={`inline-flex items-center justify-center rounded-xl px-6 py-3 text-sm font-semibold transition ${
        expired
          ? 'cursor-not-allowed border border-gray-200 bg-gray-100 text-gray-400'
          : 'border border-blue-600 bg-blue-600 text-white hover:bg-blue-700'
      }`}
      disabled={expired}
      aria-disabled={expired}
    >
      {!expired ? (
        <span className="flex items-center gap-2">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" aria-hidden="true">
            <path
              d="M12 3v12m0 0l-4-4m4 4l4-4M4 21h16"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          Download
        </span>
      ) : (
        'Fil udløbet'
      )}
    </button>
  );
}