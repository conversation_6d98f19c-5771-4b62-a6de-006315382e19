import { NextResponse, type NextRequest } from 'next/server'
import { getFilesCollection, getDownloadLogsCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'

/**
 * Redirects the user to the stored directLink after logging and enforcing limits.
 * This exposes the directLink to the browser (as requested).
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await context.params
    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID required' } as ApiResponse,
        { status: 400 }
      )
    }

    const files = await getFilesCollection()
    // Accept either public filename (preferred) or Mongo _id for backward compatibility
    let file = await files.findOne({ filename: fileId, isActive: true })
    if (!file && typeof fileId === 'string') {
      try {
        const { ObjectId } = await import('mongodb')
        if (ObjectId.isValid(fileId)) {
          const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true })
          if (byId) file = byId
        }
      } catch {
        // ignore
      }
    }

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File not found or expired' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check expiry
    if (new Date() > new Date(file.expiryDate)) {
      await files.updateOne({ _id: file._id }, { $set: { isActive: false } })
      return NextResponse.json(
        { success: false, error: 'File has expired' } as ApiResponse,
        { status: 410 }
      )
    }

    // Check download limits
    if (file.downloadLimit !== -1 && file.downloadCount >= file.downloadLimit) {
      return NextResponse.json(
        { success: false, error: 'Download limit reached' } as ApiResponse,
        { status: 429 }
      )
    }

    const directLink: string | undefined = (file as any).directLink
    if (!directLink) {
      return NextResponse.json(
        { success: false, error: 'Direct link not available for this file' } as ApiResponse,
        { status: 500 }
      )
    }

    // Log the download intent
    const downloadLogs = await getDownloadLogsCollection()
    const userAgent = request.headers.get('user-agent') || 'Unknown'
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown'

    await downloadLogs.insertOne({
      fileId: file._id.toString(),
      downloadedAt: new Date(),
      ip,
      userAgent
    })

    // Increment download count
    await files.updateOne({ _id: file._id }, { $inc: { downloadCount: 1 } })

    // Redirect to the directLink without doing anything else
    return NextResponse.redirect(directLink, { status: 302 })
  } catch (error) {
    console.error('Error processing download:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

// HEAD request for checking file availability without logging download
export async function HEAD(
  request: NextRequest,
  context: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await context.params
    const files = await getFilesCollection()
    let file = await files.findOne({ filename: fileId, isActive: true })
    if (!file && typeof fileId === 'string') {
      try {
        const { ObjectId } = await import('mongodb')
        if (ObjectId.isValid(fileId)) {
          const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true })
          if (byId) file = byId
        }
      } catch {
        // ignore
      }
    }
    
    if (!file || new Date() > new Date(file.expiryDate)) {
      return new NextResponse(null, { status: 404 })
    }

    // Optionally, probe upstream to ensure link is live without revealing it.
    // We avoid sending the actual URL to client; just reflect availability.
    // Skipping upstream HEAD to minimize latency and dependency.

    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Length': file.size?.toString() || '0',
        'Content-Type': file.mimeType || 'application/octet-stream',
        'Cache-Control': 'no-cache'
      }
    })
  } catch (error) {
    return new NextResponse(null, { status: 500 })
  }
}