import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('=== TEST UPLOAD ENDPOINT ===')
    console.log('Headers:', Object.fromEntries(request.headers.entries()))
    console.log('Method:', request.method)
    console.log('URL:', request.url)
    console.log('Body used:', request.bodyUsed)

    const formData = await request.formData()
    console.log('FormData parsed successfully')
    console.log('FormData keys:', Array.from(formData.keys()))
    
    const file = formData.get('file') as File
    if (file) {
      console.log('File found:', {
        name: file.name,
        size: file.size,
        type: file.type
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Test upload successful',
      fileInfo: file ? {
        name: file.name,
        size: file.size,
        type: file.type
      } : null
    })

  } catch (error) {
    console.error('Test upload error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
