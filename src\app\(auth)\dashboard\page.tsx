"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Progress } from "@/app/components/ui/progress"
import { Upload, File, Download, Calendar, Settings, TrendingUp, X, Copy, Trash2, Edit, MoreVertical } from "lucide-react"
import { formatFileSize, formatDate, calculateUsagePercentage, getPlanDisplayName } from "@/app/lib/utils"
import { PLAN_CONFIGS } from "@/app/lib/plans"
import { FileRecord, UsageStats } from "@/app/lib/types"
import RequestMoreUsageModal from "@/app/components/RequestMoreUsageModal"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [files, setFiles] = useState<FileRecord[]>([])
  const [usage, setUsage] = useState<UsageStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isDragOver, setIsDragOver] = useState(false)
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Modal: Request more usage
  const [isRequestOpen, setIsRequestOpen] = useState(false)
  const [requestAmount, setRequestAmount] = useState<string>("")
  const [requestMessage, setRequestMessage] = useState<string>("")
  const [requestSubmitting, setRequestSubmitting] = useState(false)

  const resetRequestForm = () => {
    setRequestAmount("")
    setRequestMessage("")
  }

  const submitRequest = async () => {
    try {
      setRequestSubmitting(true)
      // Placeholder submit - no backend endpoint yet.
      // You can connect this to an API route later.
      console.log("Requesting more usage:", {
        amount: requestAmount,
        message: requestMessage,
      })
      alert("Din forespørgsel er sendt. Vi vender tilbage hurtigst muligt.")
      setIsRequestOpen(false)
      resetRequestForm()
    } catch (e) {
      alert("Kunne ikke sende forespørgslen.")
    } finally {
      setRequestSubmitting(false)
    }
  }

  useEffect(() => {
    if (status === "loading") return
    
    if (!session) {
      router.push('/login')
      return
    }

    // Load user data
    loadDashboardData()
  }, [session, status, router])

  const loadDashboardData = async () => {
    try {
      // Load usage stats
      const usageResponse = await fetch('/api/user/usage')
      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setUsage(usageData.data)
      }

      // Load files
      const filesResponse = await fetch('/api/files')
      if (filesResponse.ok) {
        const filesData = await filesResponse.json()
        setFiles(filesData.data || [])
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  // Helper to detect if DataTransfer contains any directory entries
  const hasDirectoryInDataTransfer = (dt: DataTransfer): boolean => {
    // Prefer Chromium-specific webkitGetAsEntry if available
    const items = (dt.items ? Array.from(dt.items) : []) as DataTransferItem[]
    for (const item of items) {
      if (item.kind === "file" && typeof (item as any).webkitGetAsEntry === "function") {
        const entry = (item as any).webkitGetAsEntry()
        if (entry && entry.isDirectory) {
          return true
        }
      }
    }
    // Fallback heuristic: some browsers put a single File with size 0 and empty type for folders,
    // but this is not reliable; we rely primarily on webkitGetAsEntry.
    return false
  }

  const [folderModalOpen, setFolderModalOpen] = useState(false)

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    // Block directories; show modal instead of accepting drop
    if (hasDirectoryInDataTransfer(e.dataTransfer)) {
      setFolderModalOpen(true)
      return
    }

    const files = Array.from(e.dataTransfer.files)
    handleFileSelection(files)
  }, [])

  const handleFileSelection = (files: File[]) => {
    // Normalize user plan safely to avoid undefined planConfig
    const rawPlan = (((session?.user as any)?.plan as string) || 'free').toLowerCase()
    const PLAN_ALIASES: Record<string, keyof typeof PLAN_CONFIGS> = {
      guest: 'guest',
      free: 'free',
      gratis: 'free',
      basis: 'upgrade1',
      basic: 'upgrade1',
      upgrade1: 'upgrade1',
      pro: 'upgrade2',
      upgrade2: 'upgrade2'
    }
    const safePlan = (PLAN_ALIASES[rawPlan] ?? (rawPlan in PLAN_CONFIGS ? (rawPlan as keyof typeof PLAN_CONFIGS) : 'free')) as keyof typeof PLAN_CONFIGS
    const planConfig = PLAN_CONFIGS[safePlan]
    const maxFileSize = planConfig.uploadLimit.amount

    const validFiles = files.filter(file => file.size <= maxFileSize)
    const oversizedFiles = files.filter(file => file.size > maxFileSize)

    if (oversizedFiles.length > 0) {
      alert(`${oversizedFiles.length} fil(er) er større end ${formatFileSize(maxFileSize)} og kan ikke uploades.`)
    }

    setSelectedFiles(prev => [...prev, ...validFiles])
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files)
      handleFileSelection(files)
    }
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return

    setIsUploading(true)
    setUploadProgress(0)

    try {
      const uploadedFiles: Array<{ name: string; fileId?: string; downloadUrl?: string }> = []

      // Calculate total bytes for all files to show true cumulative progress
      const totalBytes = selectedFiles.reduce((sum, f) => sum + f.size, 0)
      let uploadedBytesSoFar = 0

      // Helper to upload a single file with XMLHttpRequest to get real progress events
      const uploadSingleFile = (file: File) =>
        new Promise<{ fileId?: string; downloadUrl?: string }>((resolve, reject) => {
          const formData = new FormData()
          // Include filename explicitly to ensure proper multipart parsing on the server
          formData.append('file', file, file.name)

          const xhr = new XMLHttpRequest()
          xhr.open('POST', '/api/upload')
          // Expect text and parse manually to avoid XHR auto parsing quirks
          xhr.responseType = 'text'
          // Let the browser set multipart boundary; only hint we accept JSON
          xhr.setRequestHeader('Accept', 'application/json')

          // Progress: update percentage using cumulative bytes across all files
          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const currentFileLoaded = event.loaded
              const otherFilesBytes = uploadedBytesSoFar
              const percent = Math.min(
                100,
                Math.round(((otherFilesBytes + currentFileLoaded) / totalBytes) * 100)
              )
              setUploadProgress(percent)
            }
          }

          xhr.onload = () => {
            // When this file is done, add its bytes to the cumulative uploaded total
            uploadedBytesSoFar += file.size

            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const json = JSON.parse(xhr.responseText)
                resolve({
                  fileId: json?.data?.fileId,
                  downloadUrl: json?.data?.downloadUrl,
                })
              } catch (e) {
                resolve({})
              }
            } else {
              try {
                const err = JSON.parse(xhr.responseText)
                reject(new Error(err?.error || 'Upload failed'))
              } catch {
                reject(new Error('Upload failed'))
              }
            }
          }

          xhr.onerror = () => {
            reject(new Error('Network error during upload'))
          }

          xhr.send(formData)
        })

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i]
        const result = await uploadSingleFile(file)
        uploadedFiles.push({
          name: file.name,
          fileId: result.fileId,
          downloadUrl: result.downloadUrl,
        })
      }

      // Ensure progress shows 100% at the end
      setUploadProgress(100)
      setIsUploading(false)
      setSelectedFiles([])

      // Reload dashboard data
      await loadDashboardData()

      // Success message
      alert(`${uploadedFiles.length} fil(er) uploadet succesfuldt!`)
    } catch (error) {
      console.error('Upload error:', error)
      setIsUploading(false)
      alert(`Upload fejlede: ${error instanceof Error ? error.message : 'Ukendt fejl'}`)
    }
  }

  const copyDownloadLink = async (file: FileRecord) => {
    try {
      // Build absolute URL to public download page
      const base = typeof window !== 'undefined'
        ? window.location.origin
        : ''
      const downloadUrl = `${base}/d/${file._id}`
      await navigator.clipboard.writeText(downloadUrl)
      alert('Download link kopieret til udklipsholder!')
    } catch (error) {
      console.error('Error copying link:', error)
      alert('Kunne ikke kopiere download link')
    }
  }

  const deleteFile = async (file: FileRecord) => {
    if (!confirm(`Er du sikker på, at du vil slette "${file.originalName}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/files/${file._id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Delete failed')
      }

      // Reload dashboard data
      await loadDashboardData()
      alert('Fil slettet succesfuldt!')
    } catch (error) {
      console.error('Delete error:', error)
      alert(`Sletning fejlede: ${error instanceof Error ? error.message : 'Ukendt fejl'}`)
    }
  }

  const toggleDropdown = (fileId: string) => {
    setOpenDropdown(openDropdown === fileId ? null : fileId)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown && !(event.target as Element).closest('.dropdown-container')) {
        setOpenDropdown(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [openDropdown])

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Indlæser dashboard...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null // Will redirect to login
  }

  // Normalize user plan (handle aliases like 'basis'/'pro') - single source of truth
  const rawPlan = (((session.user as any)?.plan as string) || 'free').toLowerCase()
  const PLAN_ALIASES: Record<string, keyof typeof PLAN_CONFIGS> = {
    guest: 'guest',
    free: 'free',
    gratis: 'free',
    basis: 'upgrade1',
    basic: 'upgrade1',
    upgrade1: 'upgrade1',
    pro: 'upgrade2',
    upgrade2: 'upgrade2'
  }
  const safePlan = (PLAN_ALIASES[rawPlan] ?? (rawPlan in PLAN_CONFIGS ? (rawPlan as keyof typeof PLAN_CONFIGS) : 'free')) as keyof typeof PLAN_CONFIGS
  const planConfig = PLAN_CONFIGS[safePlan]
  const usagePercentage = usage && planConfig ? calculateUsagePercentage(usage.current, planConfig.uploadLimit.amount) : 0

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
              Velkommen tilbage
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold mb-4 sm:mb-6 leading-tight">
              Hej <span className="text-blue-300">{session.user?.name}</span>
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Administrer dine filer og se din forbrugsstatistik
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        {/* Usage Overview */}
        {usage && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Upload forbrug</span>
                </CardTitle>
                <CardDescription>
                  Din nuværende {usage.period} forbrug
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold">
                      {formatFileSize(usage.current)} / {formatFileSize(usage.limit)}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {usage.percentage.toFixed(1)}% brugt
                    </span>
                  </div>
                  <Progress value={usage.percentage} className="h-3" />
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Plan: {getPlanDisplayName(usage.plan)}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsRequestOpen(true)}
                      className="ml-4"
                    >
                      Anmod om mere
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Plan status</CardTitle>
                <CardDescription>
                  Din nuværende plan og fordele
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-semibold">{planConfig.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    • {formatFileSize(planConfig.uploadLimit.amount)} per {
                      planConfig.uploadLimit.period === 'uge' ? 'uge' : 'måned'
                    }
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    • {planConfig.fileExpiry} dages opbevaring
                  </div>
                  {'price' in planConfig && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      • {planConfig.price.amount} kr/måned
                    </div>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4 w-full"
                    onClick={() => router.push('/pricing')}
                  >
                    Skift plan
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Upload Area */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload nye filer</span>
            </CardTitle>
            <CardDescription>
              Drag og slip filer her eller klik for at vælge filer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div
              className={`border-2 border-dashed transition-all duration-300 ${
                isDragOver
                  ? 'border-blue-500 bg-blue-100/50 scale-105'
                  : 'border-gray-300 dark:border-gray-700 hover:border-blue-500'
              } rounded-lg p-8 text-center cursor-pointer`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className={`w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                isDragOver ? 'scale-110 bg-blue-700' : ''
              }`}>
                <Upload className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">
                {isDragOver ? 'Slip filerne her!' : 'Drag filer hertil eller klik for at uploade'}
              </h3>
              <p className="text-gray-600 mb-2">
                Maksimal filstørrelse: {formatFileSize(planConfig.uploadLimit.amount)}
              </p>
              <p className="text-xs text-gray-500">
                Kun filer er tilladt. Mapper kan ikke uploades via drag-and-drop.
              </p>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={handleFileInputChange}
                accept="*/*"
              />

              <Button disabled={isUploading}>
                {isUploading ? 'Uploader...' : 'Vælg filer'}
              </Button>
            </div>

            {/* Selected Files Display */}
            {selectedFiles.length > 0 && (
              <div className="mt-8 border-t pt-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">Valgte filer ({selectedFiles.length})</h4>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <File className="h-5 w-5 text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-800 truncate max-w-xs">{file.name}</p>
                          <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => removeFile(index)}
                        className="text-red-500 hover:text-red-700 transition-colors"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>

                {/* Upload Progress */}
                {isUploading && (
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                      <span>Uploader filer...</span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Upload Button */}
                {!isUploading && (
                  <Button
                    onClick={handleUpload}
                    className="mt-4 w-full bg-green-600 hover:bg-green-700"
                  >
                    Upload {selectedFiles.length} fil{selectedFiles.length !== 1 ? 'er' : ''}
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Folder blocked modal */}
        {folderModalOpen && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
            role="dialog"
            aria-modal="true"
          >
            <Card className="w-full max-w-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-bold">
                  Mapper er ikke tilgængelige
                </CardTitle>
                <button
                  onClick={() => setFolderModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  aria-label="Luk"
                >
                  <X className="h-5 w-5" />
                </button>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                  Mappe-upload er ikke tilgængelig. Vælg venligst individuelle filer.
                </p>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setFolderModalOpen(false)}
                  >
                    Luk
                  </Button>
                  <Button 
                    onClick={() => { 
                      setFolderModalOpen(false); 
                      fileInputRef.current?.click() 
                    }}
                  >
                    Vælg filer
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Files Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <File className="h-5 w-5" />
              <span>Mine filer</span>
            </CardTitle>
            <CardDescription>
              Administrer dine uploadede filer og download-grænser
            </CardDescription>
          </CardHeader>
          <CardContent>
            {files.length === 0 ? (
              <div className="text-center py-8">
                <File className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Ingen filer endnu</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Upload din første fil for at se den her
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="text-left py-3 px-4">Filnavn</th>
                      <th className="text-left py-3 px-4">Størrelse</th>
                      <th className="text-left py-3 px-4">Upload dato</th>
                      <th className="text-left py-3 px-4">Udløber</th>
                      <th className="text-left py-3 px-4">Downloads</th>
                      <th className="text-left py-3 px-4">Grænse</th>
                      <th className="text-right py-3 px-4">Handlinger</th>
                    </tr>
                  </thead>
                  <tbody>
                    {files.map((file) => (
                      <tr key={file._id} className="border-b border-gray-100 dark:border-gray-800">
                        <td className="py-3 px-4">
                          <div className="font-medium">{file.originalName}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">{file.mimeType}</div>
                        </td>
                        <td className="py-3 px-4">{formatFileSize(file.size)}</td>
                        <td className="py-3 px-4">{formatDate(file.uploadDate)}</td>
                        <td className="py-3 px-4">{formatDate(file.expiryDate)}</td>
                        <td className="py-3 px-4">{file.downloadCount}</td>
                        <td className="py-3 px-4">
                          {file.downloadLimit === -1 ? 'Ubegrænset' : file.downloadLimit}
                        </td>
                        <td className="py-3 px-4 text-right">
                          <div className="relative dropdown-container">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => toggleDropdown(file._id)}
                              className="hover:bg-gray-100 dark:hover:bg-gray-100"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                            
                            {openDropdown === file._id && (
                              <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md shadow-md z-10">
                                <div className="py-1">
                                  <button
                                    onClick={() => {
                                      copyDownloadLink(file)
                                      setOpenDropdown(null)
                                    }}
                                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-800 flex items-center space-x-2 text-gray-700 dark:text-gray-300"
                                  >
                                    <Copy className="h-4 w-4" />
                                    <span>Kopier download link</span>
                                  </button>
                                  
                                  <button
                                    onClick={() => {
                                      deleteFile(file)
                                      setOpenDropdown(null)
                                    }}
                                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-800 flex items-center space-x-2 text-red-500 dark:text-red-400"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    <span>Slet fil</span>
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Request More Modal componentized */}
      <RequestMoreUsageModal
        open={isRequestOpen}
        onOpenChange={(o: boolean) => {
          setIsRequestOpen(o)
          if (!o) resetRequestForm()
        }}
        onSubmit={(amount: string, message: string) => {
          setRequestAmount(amount)
          setRequestMessage(message)
          submitRequest()
        }}
        submitting={requestSubmitting}
        amount={requestAmount}
        message={requestMessage}
        setAmount={setRequestAmount}
        setMessage={setRequestMessage}
        onResetForm={resetRequestForm}
      />
    </main>
  )
}