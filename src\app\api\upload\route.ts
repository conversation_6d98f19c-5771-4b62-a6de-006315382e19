import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getFilesCollection, getUsersCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'
import { 
  generateFileId, 
  getFileExpiryDate, 
  canUserUpload, 
  isValidFileType,
  getCurrentPeriodStart
} from '@/app/lib/utils'
import { ApiResponse, FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
export const maxDuration = 300 // 5 minutes for large file uploads

export async function POST(request: NextRequest) {
  try {
    // Debug request details
    console.log('Request headers:', Object.fromEntries(request.headers.entries()))
    console.log('Request method:', request.method)
    console.log('Request URL:', request.url)
    console.log('Request body locked:', request.bodyUsed)

    let formData: FormData
    try {
      // Check if body is already consumed
      if (request.bodyUsed) {
        console.error('Request body has already been consumed')
        return NextResponse.json(
          { success: false, error: 'Request body already consumed' } as ApiResponse,
          { status: 400 }
        )
      }

      // Try to get raw body first for debugging
      const clonedRequest = request.clone()
      try {
        const arrayBuffer = await clonedRequest.arrayBuffer()
        console.log('Raw body size:', arrayBuffer.byteLength)
        console.log('Raw body first 100 bytes:', new Uint8Array(arrayBuffer.slice(0, 100)))
      } catch (e) {
        console.log('Could not read raw body:', e)
      }

      formData = await request.formData()
    } catch (error) {
      console.error('Failed to parse FormData:', error)
      console.error('Error name:', error instanceof Error ? error.name : 'Unknown')
      console.error('Error message:', error instanceof Error ? error.message : String(error))
      console.error('Request body type:', typeof request.body)
      console.error('Content-Length header:', request.headers.get('content-length'))
      console.error('Content-Type header:', request.headers.get('content-type'))

      // Check if it's a size limit issue
      const contentLength = parseInt(request.headers.get('content-length') || '0')
      if (contentLength > 100 * 1024 * 1024) { // 100MB - Next.js App Router has strict limits
        return NextResponse.json(
          { success: false, error: 'File too large. Maximum size is 100MB per file due to Next.js limitations. For larger files, please use a different upload method.' } as ApiResponse,
          { status: 413 }
        )
      }

      return NextResponse.json(
        { success: false, error: 'Failed to parse form data' } as ApiResponse,
        { status: 400 }
      )
    }

    const file = formData.get('file') as unknown as File

    if (!file) {
      console.error('No file found in FormData. Available keys:', Array.from(formData.keys()))
      return NextResponse.json(
        { success: false, error: 'No file provided' } as ApiResponse,
        { status: 400 }
      )
    }

    // Log file details for debugging
    console.log('File details:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    })

    // Validate file type
    if (!file || typeof file.type !== 'string' || !isValidFileType(file.type)) {
      console.error('File type validation failed:', { type: file.type, isString: typeof file.type === 'string' })
      return NextResponse.json(
        { success: false, error: 'File type not allowed' } as ApiResponse,
        { status: 400 }
      )
    }

    // Now check authentication after successful FormData parsing
    const session = await auth()

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check upload limits
    const planConfig = PLAN_CONFIGS[user.plan as keyof typeof PLAN_CONFIGS]
    const currentUsage = user.plan === 'free' ? user.usage.monthly : user.usage.weekly
    const uploadCheck = canUserUpload(user.plan, currentUsage, file.size)
    
    if (!uploadCheck.canUpload) {
      return NextResponse.json(
        { success: false, error: uploadCheck.reason } as ApiResponse,
        { status: 400 }
      )
    }

    // Upload to GoFile.io
    const goFileFormData = new FormData()
    goFileFormData.append('file', file)

    // Add authentication token and account ID
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN
    const accountId = process.env.GOFILE_ACCOUNT_ID

    if (accountToken) {
      goFileFormData.append('token', accountToken)
    }

    // Try to get the root folder for the account first
    let folderId = null
    if (accountToken && accountId) {
      try {
        const accountResponse = await fetch(`https://api.gofile.io/accounts/${accountId}?token=${accountToken}`)
        const responseText = await accountResponse.text()

        try {
          const accountData = JSON.parse(responseText)
          if (accountData.status === 'ok' && accountData.data?.rootFolder) {
            folderId = accountData.data.rootFolder
            goFileFormData.append('folderId', folderId)
          }
        } catch (parseError) {
          console.log('Failed to parse account response:', responseText)
        }
      } catch (err) {
        console.log('Failed to get account details:', err)
      }
    }

    // Get the best server for upload
    let uploadServer = 'store1.gofile.io'
    try {
      const serverResponse = await fetch('https://api.gofile.io/servers')
      const serverData = await serverResponse.json()

      if (serverData.status === 'ok' && serverData.data?.servers?.length > 0) {
        uploadServer = serverData.data.servers[0].name + '.gofile.io'
      }
    } catch (err) {
      console.log('Failed to get servers, using default:', err)
    }

    // Upload to GoFile.io
    const uploadUrl = `https://${uploadServer}/contents/uploadfile`
    const goFileResponse = await fetch(uploadUrl, {
      method: 'POST',
      body: goFileFormData,
    })

    const goFileResult = await goFileResponse.json()
    
    // Debug the GoFile upload response
    console.log('GoFile upload response:', goFileResult)

    if (!goFileResponse.ok) {
      return NextResponse.json(
        { success: false, error: 'GoFile.io upload failed', details: goFileResult } as ApiResponse,
        { status: goFileResponse.status }
      )
    }

    // Create file record in database
    const fileId = generateFileId()
    const expiryDate = getFileExpiryDate(user.plan)

    // Build the true direct download link using GoFile getFile details (ensures UUID path).
    // 1) Prefer directLink from upload response if present and of correct form.
    // 2) Otherwise, if we have a 'code' (short code), call getFile to fetch fileId (UUID) + server.
    // 3) Construct: https://{server}.gofile.io/download/web/{fileId}/{encodedOriginalName}
    const originalNameSanitized = file.name.replace(/\//g, '_')
    let preferDirectFromUpload: string | undefined = goFileResult?.data?.directLink

    // GoFile returns 'id' for the UUID, not 'fileId'
    // GoFile returns 'servers' array, not 'server' string
    let resolvedServer: string | undefined = goFileResult?.data?.server || uploadServer
    if (goFileResult?.data?.servers && goFileResult.data.servers.length > 0) {
      resolvedServer = goFileResult.data.servers[0] + '.gofile.io'
    }
    let resolvedFileIdUuid: string | undefined = goFileResult?.data?.id || goFileResult?.data?.fileId
    const shortCode: string | undefined = goFileResult?.data?.code || goFileResult?.data?.parentFolderCode

    // If we lack a UUID fileId, but have a short code, query GoFile getFile
    if (!resolvedFileIdUuid && shortCode) {
      try {
        const baseUrl = process.env.GOFILE_API_BASE_URL || 'https://api.gofile.io'
        const accountToken = process.env.GOFILE_ACCOUNT_TOKEN
        const infoUrl = `${baseUrl}/getFile/${shortCode}${accountToken ? `?token=${accountToken}` : ''}`
        console.log('Calling getFile API:', infoUrl)
        const infoResp = await fetch(infoUrl, { method: 'GET' })
        const infoJson = await infoResp.json()
        console.log('getFile API response:', infoJson)
        
        if (infoResp.ok && infoJson?.status === 'ok' && infoJson?.data) {
          // Typical fields: { server, fileId, directLink, downloadPage, ... }
          resolvedServer = infoJson.data.server || resolvedServer
          resolvedFileIdUuid = infoJson.data.fileId || resolvedFileIdUuid
          
          // If GoFile returns directLink here, prefer it
          if (!preferDirectFromUpload && infoJson.data.directLink) {
            preferDirectFromUpload = infoJson.data.directLink
          }
        }
      } catch (e) {
        console.log('Failed to resolve GoFile file info via getFile:', e)
      }
    }

    // Construct direct link safely if we have server + UUID
    const safeServer = resolvedServer?.includes('gofile.io') ? resolvedServer : `${resolvedServer}.gofile.io`
    const constructedDirect = (safeServer && resolvedFileIdUuid)
      ? `https://${safeServer}/download/web/${resolvedFileIdUuid}/${encodeURIComponent(originalNameSanitized)}`
      : undefined

    // Debug logging
    console.log('GoFile upload result:', {
      server: resolvedServer,
      fileId: resolvedFileIdUuid,
      shortCode,
      preferDirectFromUpload,
      constructedDirect,
      goFileResult: goFileResult?.data
    })

    // Prioritize direct links in this order:
    // 1. Direct link from upload response (if it's a proper direct link)
    // 2. Constructed direct link (if we have server + UUID)
    // 3. Direct link from getFile API call
    // 4. Fallback to download page
    const directLink: string | undefined =
      preferDirectFromUpload?.includes('/download/web/')
        ? preferDirectFromUpload
        : constructedDirect || goFileResult?.data?.directLink || goFileResult?.data?.downloadPage;

    const fileRecord: Omit<FileRecord, '_id'> = {
      filename: fileId,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      ownerId: user._id.toString(),
      uploadDate: new Date(),
      expiryDate: expiryDate,
      downloadCount: 0,
      downloadLimit: planConfig.downloadLimits.unlimited ? -1 : 10,
      isActive: true,
      storageProvider: 'gofile',
      // Store GoFile identifiers: prefer UUID fileId if available, otherwise short code
      storageKey: (goFileResult.data?.fileId || goFileResult.data?.code || fileId),
      directLink
    }

    const files = await getFilesCollection()
    const result = await files.insertOne(fileRecord)

    // Update user usage
    const usageField = user.plan === 'free' ? 'usage.monthly' : 'usage.weekly'
    await users.updateOne(
      { _id: user._id },
      { $inc: { [usageField]: file.size } }
    )

    const createdFile = await files.findOne({ _id: result.insertedId })

    return NextResponse.json({
      success: true,
      data: {
        fileId: createdFile?._id.toString(),
        filename: file.name,
        downloadUrl: createdFile?.directLink || constructedDirect || goFileResult?.data?.directLink || goFileResult?.data?.downloadPage,
        expiryDate: expiryDate,
        size: file.size
      },
      message: 'File uploaded successfully'
    } as ApiResponse)

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
} 